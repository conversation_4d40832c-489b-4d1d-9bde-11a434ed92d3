# import copy

# name = "张三"
# print(f"我的名字是{name}")

# age = 18
# print("我的年龄是%s岁" % age)

# s1 = (1,2,3)
# print(s1)

# s2 = [1,2,3]
# s2.clear()
# print(s2)

# a = 100
# a = "Hello"
# print(a)

# s3 = {1,2,3}
# s3.update({1,2,3,4,5})
# s3.update([11,22,33])
# print(s3)

# s4 = copy.copy(s3)
# print(s4)
# print(id(s3))
# print(id(s4))

# s5 = [1,2, [3,4]]
# s6 = copy.deepcopy(s5)
# print(s6)
# print(id(s5))
# print(id(s6))
# print(id(s5[2]))
# print(id(s6[2]))

# s7 = copy.copy(s5)
# print(s7)
# print(id(s5))
# print(id(s7))
# print(id(s5[2]))
# print(id(s7[2]))

# 默认参数
def sum(a, b):
  print(f"a={a}, b={b}")
  return a + b

print(sum(1, 2))

print("姓名:", "张三", "年龄:", 18)