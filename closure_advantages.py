"""
Python闭包的优势演示
闭包是指一个函数能够访问其外部作用域中的变量，即使外部函数已经执行完毕
"""

print("=== Python闭包的优势演示 ===\n")

# 1. 数据封装和状态保持
print("1. 数据封装和状态保持")
def create_counter(initial_value=0):
    """创建一个计数器，保持内部状态"""
    count = initial_value
    
    def counter():
        nonlocal count
        count += 1
        return count
    
    def get_count():
        return count
    
    def reset():
        nonlocal count
        count = initial_value
    
    # 返回多个函数，形成闭包
    return counter, get_count, reset

# 创建两个独立的计数器
counter1, get_count1, reset1 = create_counter(0)
counter2, get_count2, reset2 = create_counter(100)

print(f"计数器1: {counter1()}, {counter1()}, {counter1()}")  # 1, 2, 3
print(f"计数器2: {counter2()}, {counter2()}")  # 101, 102
print(f"计数器1当前值: {get_count1()}")
print(f"计数器2当前值: {get_count2()}")
print()

# 2. 函数工厂 - 创建配置化的函数
print("2. 函数工厂 - 创建配置化的函数")
def create_multiplier(factor):
    """创建一个乘法器函数"""
    def multiply(number):
        return number * factor
    return multiply

# 创建不同的乘法器
double = create_multiplier(2)
triple = create_multiplier(3)
square = create_multiplier(2)  # 平方

print(f"2的2倍: {double(2)}")
print(f"3的3倍: {triple(3)}")
print(f"4的平方: {square(4)}")
print()

# 3. 回调函数和事件处理
print("3. 回调函数和事件处理")
def create_event_handler(event_name):
    """创建事件处理器"""
    def handler(data):
        print(f"处理事件 '{event_name}': {data}")
    return handler

# 创建不同的事件处理器
login_handler = create_event_handler("用户登录")
logout_handler = create_event_handler("用户登出")
error_handler = create_event_handler("系统错误")

login_handler({"user": "张三", "time": "2024-01-01"})
logout_handler({"user": "张三"})
error_handler({"error": "数据库连接失败"})
print()

# 4. 装饰器的基础
print("4. 装饰器的基础")
def create_logger(func_name):
    """创建日志记录器"""
    def logger(func):
        def wrapper(*args, **kwargs):
            print(f"[{func_name}] 开始执行")
            result = func(*args, **kwargs)
            print(f"[{func_name}] 执行完成，结果: {result}")
            return result
        return wrapper
    return logger

# 使用闭包创建装饰器
@create_logger("计算函数")
def add(a, b):
    return a + b

result = add(3, 5)
print()

# 5. 配置管理
print("5. 配置管理")
def create_config_manager(config):
    """创建配置管理器"""
    def get_config(key, default=None):
        return config.get(key, default)
    
    def set_config(key, value):
        config[key] = value
    
    def list_config():
        return config.copy()
    
    return get_config, set_config, list_config

# 创建配置管理器
get_config, set_config, list_config = create_config_manager({
    "database_url": "localhost:5432",
    "debug": True,
    "max_connections": 100
})

print(f"数据库URL: {get_config('database_url')}")
print(f"调试模式: {get_config('debug')}")
set_config("timeout", 30)
print(f"所有配置: {list_config()}")
print()

# 6. 缓存和记忆化
print("6. 缓存和记忆化")
def create_cached_function(func):
    """创建带缓存的函数"""
    cache = {}
    
    def cached_func(*args):
        key = str(args)
        if key in cache:
            print(f"缓存命中: {args}")
            return cache[key]
        
        print(f"计算新值: {args}")
        result = func(*args)
        cache[key] = result
        return result
    
    def clear_cache():
        cache.clear()
        print("缓存已清空")
    
    def get_cache_info():
        return f"缓存大小: {len(cache)}, 键: {list(cache.keys())}"
    
    cached_func.clear_cache = clear_cache
    cached_func.get_cache_info = get_cache_info
    
    return cached_func

# 创建带缓存的斐波那契函数
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

cached_fib = create_cached_function(fibonacci)

print(f"fib(5): {cached_fib(5)}")
print(f"fib(5): {cached_fib(5)}")  # 从缓存获取
print(f"fib(6): {cached_fib(6)}")
print(cached_fib.get_cache_info())
print()

# 7. 闭包的优势总结
print("=== Python闭包的主要优势 ===")
print("""
1. 数据封装: 闭包可以创建私有变量，避免全局变量污染
2. 状态保持: 函数可以记住之前的状态，实现状态机
3. 函数工厂: 可以动态创建具有不同配置的函数
4. 回调机制: 在事件驱动编程中非常有用
5. 装饰器基础: 装饰器本质上就是闭包的应用
6. 配置管理: 可以创建具有特定配置的函数集合
7. 缓存机制: 实现函数结果的缓存和记忆化
8. 模块化: 将相关功能封装在一起，提高代码组织性
9. 延迟计算: 可以创建延迟执行的函数
10. 函数式编程: 支持函数式编程范式
""")

print("闭包让Python代码更加灵活、模块化和可维护！")
